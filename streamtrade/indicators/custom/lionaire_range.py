"""
Lionaire - Range Indicator

Advanced predictive range indicator with adaptive average and box overlay system.
Based on TradingView Pine Script with multiple ATR calculation methods.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import warnings

from ..base_indicator import BaseIndicator, IndicatorParameter
from ...config.logging_config import get_logger

logger = get_logger(__name__)


class LionaireRange(BaseIndicator):
    """
    Lionaire - Range Indicator
    
    Advanced predictive range indicator featuring:
    - Adaptive average with discrete movement
    - Multiple ATR calculation methods (Standard, HMA, KAMA, SMA, EMA)
    - Box overlay system with 7 levels
    - Multi-timeframe support
    - Fill areas between range lines
    """
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="calculation_length",
                type=int,
                default=200,
                min_value=2,
                max_value=1000,
                description="Calculation length for ATR and adaptive average"
            ),
            IndicatorParameter(
                name="atr_multiplier_factor",
                type=float,
                default=6.0,
                min_value=0.1,
                max_value=20.0,
                description="ATR multiplier factor for range width"
            ),
            IndicatorParameter(
                name="atr_calculation_method",
                type=str,
                default="KAMA ATR",
                options=["Standard ATR", "HMA ATR", "KAMA ATR", "SMA ATR", "EMA ATR"],
                description="ATR calculation method"
            ),
            IndicatorParameter(
                name="price_source",
                type=str,
                default="close",
                options=["close", "open", "high", "low", "hl2", "hlc3", "ohlc4"],
                description="Price source for calculations"
            ),
            IndicatorParameter(
                name="upper_color",
                type=str,
                default="#f23645",
                description="Upper range color",
                is_color=True
            ),
            IndicatorParameter(
                name="lower_color",
                type=str,
                default="#089981",
                description="Lower range color",
                is_color=True
            ),
            IndicatorParameter(
                name="middle_color",
                type=str,
                default="#797979",
                description="Middle lines color",
                is_color=True
            ),
            IndicatorParameter(
                name="fill_transparency",
                type=float,
                default=0.95,
                min_value=0.0,
                max_value=1.0,
                description="Fill area transparency (0.0 = opaque, 1.0 = transparent)"
            ),
            IndicatorParameter(
                name="enable_fills",
                type=bool,
                default=True,
                description="Enable fill areas between range lines"
            ),
            IndicatorParameter(
                name="show_middle_lines",
                type=bool,
                default=True,
                description="Show middle lines (Mid Upper, Mid Lower)"
            )
        ]
    
    def _get_description(self) -> str:
        return "Lionaire - Range: Advanced predictive range with adaptive average and box overlay"
    
    def _get_category(self) -> str:
        return "custom"
    
    def _get_display_on_chart(self) -> str:
        return "overlay"
    
    def _get_legend_parameters(self) -> List[str]:
        return ["calculation_length", "atr_multiplier_factor", "atr_calculation_method"]
    
    def _calculate_price_source(self, data: pd.DataFrame, source: str) -> pd.Series:
        """Calculate price source based on selection."""
        if source == "close":
            return data['close']
        elif source == "open":
            return data['open']
        elif source == "high":
            return data['high']
        elif source == "low":
            return data['low']
        elif source == "hl2":
            return (data['high'] + data['low']) / 2
        elif source == "hlc3":
            return (data['high'] + data['low'] + data['close']) / 3
        elif source == "ohlc4":
            return (data['open'] + data['high'] + data['low'] + data['close']) / 4
        else:
            return data['close']
    
    def _calculate_true_range(self, data: pd.DataFrame) -> pd.Series:
        """Calculate True Range (TR) for each bar."""
        high = data['high']
        low = data['low']
        close_prev = data['close'].shift(1)
        
        tr1 = high - low
        tr2 = np.abs(high - close_prev)
        tr3 = np.abs(low - close_prev)
        
        return np.maximum(tr1, np.maximum(tr2, tr3))
    
    def _hma(self, series: pd.Series, length: int) -> pd.Series:
        """Hull Moving Average implementation."""
        if length < 1:
            return series
            
        try:
            # WMA with half period
            half_length = max(1, int(length / 2))
            wma1 = series.rolling(window=half_length).apply(
                lambda x: np.sum(x * np.arange(1, len(x) + 1)) / np.sum(np.arange(1, len(x) + 1)),
                raw=True
            )
            
            # WMA with full period
            wma2 = series.rolling(window=length).apply(
                lambda x: np.sum(x * np.arange(1, len(x) + 1)) / np.sum(np.arange(1, len(x) + 1)),
                raw=True
            )
            
            # Calculate difference
            wma_diff = 2 * wma1 - wma2
            
            # Final WMA with sqrt(length)
            sqrt_length = max(1, int(np.sqrt(length)))
            hma_result = wma_diff.rolling(window=sqrt_length).apply(
                lambda x: np.sum(x * np.arange(1, len(x) + 1)) / np.sum(np.arange(1, len(x) + 1)),
                raw=True
            )
            
            return hma_result
            
        except Exception as e:
            logger.warning(f"HMA calculation error: {e}, falling back to EMA")
            return series.ewm(span=length).mean()
    
    def _kama(self, series: pd.Series, length: int, fast_alpha_period: int = 2, slow_alpha_period: int = 30) -> pd.Series:
        """Kaufman's Adaptive Moving Average implementation."""
        if length < 2:
            return series

        try:
            # Convert to numpy array for easier calculation
            values = series.values
            n = len(values)

            # Calculate change and volatility
            change = np.abs(np.diff(values, prepend=values[0]))
            volatility = np.zeros(n)

            # Calculate rolling volatility
            for i in range(length, n):
                volatility[i] = np.sum(change[i-length+1:i+1])

            # Calculate Efficiency Ratio (ER)
            direction = np.zeros(n)
            for i in range(length, n):
                direction[i] = np.abs(values[i] - values[i-length])

            er = np.where(volatility != 0, direction / volatility, 0)

            # Calculate Smoothing Constant (SC)
            fast_alpha = 2 / (fast_alpha_period + 1)
            slow_alpha = 2 / (slow_alpha_period + 1)
            sc = np.power(er * (fast_alpha - slow_alpha) + slow_alpha, 2)

            # Calculate KAMA
            kama_values = np.zeros(n)
            kama_values[0] = values[0]

            for i in range(1, n):
                if not np.isnan(sc[i]) and not np.isnan(kama_values[i-1]):
                    kama_values[i] = kama_values[i-1] + sc[i] * (values[i] - kama_values[i-1])
                else:
                    kama_values[i] = values[i]

            return pd.Series(kama_values, index=series.index)

        except Exception as e:
            logger.warning(f"KAMA calculation error: {e}, falling back to EMA")
            return series.ewm(span=length).mean()
    
    def _calculate_atr_smoothed(self, true_range: pd.Series, length: int, method: str) -> pd.Series:
        """Calculate smoothed ATR using specified method."""
        if method == "Standard ATR":
            # Wilder's smoothing (similar to ta.atr in Pine Script)
            alpha = 1.0 / length
            return true_range.ewm(alpha=alpha, adjust=False).mean()
        elif method == "HMA ATR":
            return self._hma(true_range, length)
        elif method == "KAMA ATR":
            return self._kama(true_range, length)
        elif method == "SMA ATR":
            return true_range.rolling(window=length).mean()
        elif method == "EMA ATR":
            return true_range.ewm(span=length).mean()
        else:
            # Default to Standard ATR
            alpha = 1.0 / length
            return true_range.ewm(alpha=alpha, adjust=False).mean()

    def _calculate_adaptive_average(self, price_source: pd.Series, atr_smoothed: pd.Series,
                                  atr_multiplier: float) -> pd.Series:
        """
        Calculate adaptive average with discrete movement.
        Only moves when price change > ATR threshold.
        """
        adaptive_avg = pd.Series(index=price_source.index, dtype=float)

        # Initialize first value
        adaptive_avg.iloc[0] = price_source.iloc[0]

        for i in range(1, len(price_source)):
            current_price = price_source.iloc[i]
            prev_avg = adaptive_avg.iloc[i-1]
            current_atr = atr_smoothed.iloc[i]

            # Check if price movement is significant (> ATR threshold)
            if pd.notna(current_atr) and current_atr > 0:
                price_change = abs(current_price - prev_avg)
                atr_threshold = current_atr * atr_multiplier

                if price_change > atr_threshold:
                    # Significant movement - update average
                    adaptive_avg.iloc[i] = current_price
                else:
                    # No significant movement - keep previous value
                    adaptive_avg.iloc[i] = prev_avg
            else:
                # No ATR data - use current price
                adaptive_avg.iloc[i] = current_price

        return adaptive_avg

    def _calculate_range_levels(self, adaptive_avg: pd.Series, atr_smoothed: pd.Series,
                               atr_multiplier: float) -> Dict[str, pd.Series]:
        """
        Calculate all 7 range levels based on adaptive average and ATR.

        Structure:
        - PR Upper 2 (top_outside)
        - PR Mid Upper (top_middle)
        - PR Upper 1 (top_inside)
        - PR Average (mid_line)
        - PR Lower 1 (bottom_inside)
        - PR Mid Lower (bottom_middle)
        - PR Lower 2 (bottom_outside)
        """
        # Calculate range width
        range_width = atr_smoothed * atr_multiplier

        # Calculate all levels
        pr_average = adaptive_avg
        pr_upper_1 = adaptive_avg + (range_width * 0.5)
        pr_lower_1 = adaptive_avg - (range_width * 0.5)
        pr_upper_2 = adaptive_avg + range_width
        pr_lower_2 = adaptive_avg - range_width

        # Calculate middle levels (between inner and outer)
        pr_mid_upper = (pr_upper_1 + pr_upper_2) / 2
        pr_mid_lower = (pr_lower_1 + pr_lower_2) / 2

        return {
            'pr_upper_2': pr_upper_2,      # top_outside
            'pr_mid_upper': pr_mid_upper,  # top_middle
            'pr_upper_1': pr_upper_1,      # top_inside
            'pr_average': pr_average,      # mid_line
            'pr_lower_1': pr_lower_1,      # bottom_inside
            'pr_mid_lower': pr_mid_lower,  # bottom_middle
            'pr_lower_2': pr_lower_2       # bottom_outside
        }

    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        """Calculate Lionaire - Range indicator."""
        try:
            # Get parameters
            calculation_length = kwargs.get('calculation_length', 200)
            atr_multiplier_factor = kwargs.get('atr_multiplier_factor', 6.0)
            atr_calculation_method = kwargs.get('atr_calculation_method', 'KAMA ATR')
            price_source = kwargs.get('price_source', 'close')

            # Validate required columns
            required_columns = ['high', 'low', 'close']
            for col in required_columns:
                if col not in data.columns:
                    raise ValueError(f"Missing required column: {col}")

            # Calculate price source
            source_prices = self._calculate_price_source(data, price_source)

            # Calculate True Range
            true_range = self._calculate_true_range(data)

            # Calculate smoothed ATR using selected method
            atr_smoothed = self._calculate_atr_smoothed(true_range, calculation_length, atr_calculation_method)

            # Calculate adaptive average with discrete movement
            adaptive_avg = self._calculate_adaptive_average(source_prices, atr_smoothed, atr_multiplier_factor)

            # Calculate all range levels
            range_levels = self._calculate_range_levels(adaptive_avg, atr_smoothed, atr_multiplier_factor)

            # Add additional data for visualization
            result = {
                **range_levels,
                'atr_smoothed': atr_smoothed,
                'true_range': true_range,
                'source_prices': source_prices,
                'range_width': atr_smoothed * atr_multiplier_factor
            }

            logger.debug(f"Lionaire Range calculated with {len(result)} series")
            return result

        except Exception as e:
            logger.error(f"Error calculating Lionaire Range: {str(e)}")
            # Return empty series with same index
            empty_series = pd.Series(index=data.index, dtype=float)
            return {
                'pr_upper_2': empty_series,
                'pr_mid_upper': empty_series,
                'pr_upper_1': empty_series,
                'pr_average': empty_series,
                'pr_lower_1': empty_series,
                'pr_mid_lower': empty_series,
                'pr_lower_2': empty_series,
                'atr_smoothed': empty_series,
                'true_range': empty_series,
                'source_prices': empty_series,
                'range_width': empty_series
            }
