"""
LIONAIRE - Kalman Trend Levels Indicator

Simple implementation based on TradingView Pine Script.
Uses dual Kalman filters for trend detection.
"""

import pandas as pd
import numpy as np
from typing import Dict, List

from ..base_indicator import BaseIndicator, IndicatorParameter
from ...config.logging_config import get_logger

logger = get_logger(__name__)


class KalmanTrendLevels(BaseIndicator):
    """
    LIONAIRE - Kalman Trend Levels Indicator
    
    Simple dual Kalman filter implementation for trend detection.
    Based on TradingView Pine Script version.
    """
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="short_length",
                type=int,
                default=50,
                min_value=5,
                max_value=200,
                description="Short Kalman filter length"
            ),
            IndicatorParameter(
                name="long_length",
                type=int,
                default=150,
                min_value=10,
                max_value=500,
                description="Long Kalman filter length"
            ),
            IndicatorParameter(
                name="upper_color",
                type=str,
                default="#13bd6e",
                description="Bullish color",
                is_color=True
            ),
            IndicatorParameter(
                name="lower_color",
                type=str,
                default="#af0d4b",
                description="Bearish color",
                is_color=True
            ),
            IndicatorParameter(
                name="fill_bullish_color",
                type=str,
                default="#13bd6e",
                description="Bullish fill color",
                is_color=True
            ),
            IndicatorParameter(
                name="fill_bearish_color",
                type=str,
                default="#af0d4b",
                description="Bearish fill color",
                is_color=True
            ),
            IndicatorParameter(
                name="fill_transparency",
                type=float,
                default=0.2,
                min_value=0.0,
                max_value=1.0,
                description="Fill area transparency (0.0 = transparent, 1.0 = opaque)"
            ),
            IndicatorParameter(
                name="enable_fill",
                type=bool,
                default=True,
                description="Enable fill area between Kalman lines"
            )
        ]
    
    def _get_description(self) -> str:
        return "LIONAIRE - Kalman Trend Levels: Dual Kalman filter for trend detection"
    
    def _get_category(self) -> str:
        return "trend"

    def _hex_to_rgba(self, hex_color: str, alpha: float) -> str:
        """Convert hex color to RGBA format with transparency."""
        try:
            # Remove # if present
            hex_color = hex_color.lstrip('#')

            # Convert hex to RGB
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)

            # Return RGBA format
            return f"rgba({r}, {g}, {b}, {alpha})"
        except (ValueError, IndexError):
            # Fallback to default if conversion fails
            return f"rgba(128, 128, 128, {alpha})"
    
    def _kalman_filter(self, src: pd.Series, length: int, R: float = 0.01, Q: float = 0.1) -> pd.Series:
        """
        Kalman filter implementation matching TradingView Pine Script.
        
        Args:
            src: Source price series
            length: Filter length
            R: Measurement noise
            Q: Process noise
            
        Returns:
            Filtered series
        """
        result = np.full(len(src), np.nan)
        
        # Initialize variables (matching Pine Script var declarations)
        estimate = np.nan
        error_est = 1.0
        error_meas = R * length
        
        for i in range(len(src)):
            if pd.isna(src.iloc[i]):
                result[i] = np.nan
                continue
            
            # Initialize the estimate with the first value (matching Pine Script logic)
            if np.isnan(estimate) and i > 0:
                estimate = src.iloc[i-1] if not pd.isna(src.iloc[i-1]) else src.iloc[i]
            elif np.isnan(estimate):
                estimate = src.iloc[i]
                result[i] = estimate
                continue
            
            # Prediction step
            prediction = estimate
            
            # Update Kalman gain
            kalman_gain = error_est / (error_est + error_meas)
            
            # Update estimate with measurement correction
            estimate = prediction + kalman_gain * (src.iloc[i] - prediction)
            
            # Update error estimates
            error_est = (1 - kalman_gain) * error_est + Q / length
            
            result[i] = estimate
        
        return pd.Series(result, index=src.index)
    
    def _calculate_atr(self, data: pd.DataFrame, period: int = 200) -> pd.Series:
        """Calculate ATR for the indicator."""
        high = data['high']
        low = data['low']
        close = data['close']
        
        # True Range calculation
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean() * 0.5
        
        return atr
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        """Calculate Kalman Trend Levels indicator."""
        try:
            # Get parameters
            short_length = kwargs.get('short_length', 50)
            long_length = kwargs.get('long_length', 150)
            upper_color = kwargs.get('upper_color', '#13bd6e')
            lower_color = kwargs.get('lower_color', '#af0d4b')
            
            # Validate required columns
            required_columns = ['high', 'low', 'close']
            for col in required_columns:
                if col not in data.columns:
                    raise ValueError(f"Missing required column: {col}")
            
            close_prices = data['close']
            
            # Calculate Kalman filters
            short_kalman = self._kalman_filter(close_prices, short_length)
            long_kalman = self._kalman_filter(close_prices, long_length)
            
            # Calculate ATR
            atr = self._calculate_atr(data)
            
            # Determine trend
            trend_up = short_kalman > long_kalman
            
            # Calculate trend colors (matching Pine Script logic)
            # trend_col = trend_up ? upper_col : lower_col
            trend_col = trend_up.map({True: upper_color, False: lower_color})
            
            # trend_col1 = short_kalman > short_kalman[2] ? upper_col : lower_col
            short_momentum = short_kalman > short_kalman.shift(2)
            trend_col1 = short_momentum.map({True: upper_color, False: lower_color})
            
            # Prepare results - return Kalman lines and trend info for fill area
            results = {
                'short_kalman': short_kalman,
                'long_kalman': long_kalman,
                'trend_up': trend_up  # Needed for fill area coloring
            }
            
            logger.debug(f"Calculated Kalman Trend Levels with {len(results)} series")
            return results
            
        except Exception as e:
            logger.error(f"Error calculating Kalman Trend Levels: {str(e)}")
            raise
