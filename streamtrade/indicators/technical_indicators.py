"""
Technical indicators implementation using pandas-ta.
"""

import pandas as pd
import numpy as np
from typing import Dict, List

# Use custom implementations for better compatibility
PANDAS_TA_AVAILABLE = False

from .base_indicator import BaseIndicator, IndicatorParameter
from .custom import KalmanTrendLevels
from ..config.logging_config import get_logger

logger = get_logger(__name__)

# Fallback implementations if pandas-ta is not available
def simple_sma(series: pd.Series, period: int) -> pd.Series:
    """Simple Moving Average implementation."""
    return series.rolling(window=period).mean()

def simple_ema(series: pd.Series, period: int) -> pd.Series:
    """Exponential Moving Average implementation."""
    return series.ewm(span=period).mean()

def simple_rsi(series: pd.Series, period: int = 14) -> pd.Series:
    """Relative Strength Index implementation."""
    delta = series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def simple_macd(series: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
    """MACD implementation."""
    ema_fast = simple_ema(series, fast)
    ema_slow = simple_ema(series, slow)
    macd_line = ema_fast - ema_slow
    signal_line = simple_ema(macd_line, signal)
    histogram = macd_line - signal_line

    return {
        'macd': macd_line,
        'signal': signal_line,
        'histogram': histogram
    }

def simple_bollinger_bands(series: pd.Series, period: int = 20, std: float = 2.0) -> Dict[str, pd.Series]:
    """Bollinger Bands implementation."""
    sma = simple_sma(series, period)
    rolling_std = series.rolling(window=period).std()

    upper = sma + (rolling_std * std)
    lower = sma - (rolling_std * std)

    return {
        'upper': upper,
        'middle': sma,
        'lower': lower
    }

def simple_ichimoku(high: pd.Series, low: pd.Series, close: pd.Series,
                   tenkan_period: int = 9, kijun_period: int = 26,
                   senkou_span_b_period: int = 52, chikou_span_period: int = 26) -> Dict[str, pd.Series]:
    """Ichimoku Kinko Hyo implementation."""

    # Tenkan-sen (Conversion Line): (9-period high + 9-period low) / 2
    tenkan_sen = (high.rolling(window=tenkan_period).max() + low.rolling(window=tenkan_period).min()) / 2

    # Kijun-sen (Base Line): (26-period high + 26-period low) / 2
    kijun_sen = (high.rolling(window=kijun_period).max() + low.rolling(window=kijun_period).min()) / 2

    # Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen) / 2, projected 26 periods ahead
    senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(kijun_period)

    # Senkou Span B (Leading Span B): (52-period high + 52-period low) / 2, projected 26 periods ahead
    senkou_span_b = ((high.rolling(window=senkou_span_b_period).max() +
                     low.rolling(window=senkou_span_b_period).min()) / 2).shift(kijun_period)

    # Chikou Span (Lagging Span): Close price projected 26 periods back
    chikou_span = close.shift(-chikou_span_period)

    return {
        'tenkan_sen': tenkan_sen,
        'kijun_sen': kijun_sen,
        'senkou_span_a': senkou_span_a,
        'senkou_span_b': senkou_span_b,
        'chikou_span': chikou_span
    }

def simple_stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
    """Stochastic Oscillator implementation."""
    lowest_low = low.rolling(window=k_period).min()
    highest_high = high.rolling(window=k_period).max()

    k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
    d_percent = k_percent.rolling(window=d_period).mean()

    return {
        'k': k_percent,
        'd': d_percent
    }

def simple_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
    """Average True Range implementation."""
    tr1 = high - low
    tr2 = abs(high - close.shift())
    tr3 = abs(low - close.shift())

    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = true_range.rolling(window=period).mean()

    return atr


class SMA(BaseIndicator):
    """Simple Moving Average indicator."""

    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=20,
                min_value=1,
                max_value=200,
                description="Number of periods for moving average"
            ),
            IndicatorParameter(
                name="color",
                type=str,
                default="#1f77b4",
                description="Line color",
                is_color=True
            )
        ]
    
    def _get_description(self) -> str:
        return "Simple Moving Average - arithmetic mean of prices over specified periods"
    
    def _get_category(self) -> str:
        return "trend"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']

        if PANDAS_TA_AVAILABLE:
            try:
                sma = ta.sma(data['close'], length=period)
            except Exception:
                sma = simple_sma(data['close'], period)
        else:
            sma = simple_sma(data['close'], period)

        return {'sma': sma}


class EMA(BaseIndicator):
    """Exponential Moving Average indicator."""

    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=20,
                min_value=1,
                max_value=200,
                description="Number of periods for exponential moving average"
            ),
            IndicatorParameter(
                name="color",
                type=str,
                default="#ff7f0e",
                description="Line color",
                is_color=True
            )
        ]
    
    def _get_description(self) -> str:
        return "Exponential Moving Average - weighted average giving more importance to recent prices"
    
    def _get_category(self) -> str:
        return "trend"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']

        if PANDAS_TA_AVAILABLE:
            try:
                ema = ta.ema(data['close'], length=period)
            except Exception:
                ema = simple_ema(data['close'], period)
        else:
            ema = simple_ema(data['close'], period)

        return {'ema': ema}


class RSI(BaseIndicator):
    """Relative Strength Index indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=14,
                min_value=2,
                max_value=100,
                description="Number of periods for RSI calculation"
            )
        ]
    
    def _get_description(self) -> str:
        return "Relative Strength Index - momentum oscillator measuring speed and change of price movements"
    
    def _get_category(self) -> str:
        return "momentum"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']

        if PANDAS_TA_AVAILABLE:
            try:
                rsi = ta.rsi(data['close'], length=period)
            except Exception:
                rsi = simple_rsi(data['close'], period)
        else:
            rsi = simple_rsi(data['close'], period)

        return {'rsi': rsi}


class MACD(BaseIndicator):
    """MACD (Moving Average Convergence Divergence) indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="fast",
                type=int,
                default=12,
                min_value=1,
                max_value=50,
                description="Fast EMA period"
            ),
            IndicatorParameter(
                name="slow",
                type=int,
                default=26,
                min_value=1,
                max_value=100,
                description="Slow EMA period"
            ),
            IndicatorParameter(
                name="signal",
                type=int,
                default=9,
                min_value=1,
                max_value=50,
                description="Signal line EMA period"
            )
        ]
    
    def _get_description(self) -> str:
        return "MACD - trend-following momentum indicator showing relationship between two moving averages"
    
    def _get_category(self) -> str:
        return "momentum"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        fast = kwargs['fast']
        slow = kwargs['slow']
        signal = kwargs['signal']

        macd_data = simple_macd(data['close'], fast=fast, slow=slow, signal=signal)

        return macd_data


class BollingerBands(BaseIndicator):
    """Bollinger Bands indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=20,
                min_value=2,
                max_value=100,
                description="Number of periods for moving average"
            ),
            IndicatorParameter(
                name="std",
                type=float,
                default=2.0,
                min_value=0.1,
                max_value=5.0,
                description="Number of standard deviations"
            ),
            IndicatorParameter(
                name="upper_color",
                type=str,
                default="#ff0000",
                description="Upper band color",
                is_color=True
            ),
            IndicatorParameter(
                name="middle_color",
                type=str,
                default="#0000ff",
                description="Middle line (SMA) color",
                is_color=True
            ),
            IndicatorParameter(
                name="lower_color",
                type=str,
                default="#ff0000",
                description="Lower band color",
                is_color=True
            )
        ]
    
    def _get_description(self) -> str:
        return "Bollinger Bands - volatility indicator with upper and lower bands around moving average"
    
    def _get_category(self) -> str:
        return "volatility"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']
        std = kwargs['std']

        bb_data = simple_bollinger_bands(data['close'], period=period, std=std)

        return bb_data


class Stochastic(BaseIndicator):
    """Stochastic Oscillator indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="k_period",
                type=int,
                default=14,
                min_value=1,
                max_value=100,
                description="K period for stochastic calculation"
            ),
            IndicatorParameter(
                name="d_period",
                type=int,
                default=3,
                min_value=1,
                max_value=50,
                description="D period for signal line"
            )
        ]
    
    def _get_description(self) -> str:
        return "Stochastic Oscillator - momentum indicator comparing closing price to price range"
    
    def _get_category(self) -> str:
        return "momentum"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        k_period = kwargs['k_period']
        d_period = kwargs['d_period']

        stoch_data = simple_stochastic(
            data['high'], data['low'], data['close'],
            k_period=k_period, d_period=d_period
        )

        return stoch_data


class ATR(BaseIndicator):
    """Average True Range indicator."""
    
    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="period",
                type=int,
                default=14,
                min_value=1,
                max_value=100,
                description="Number of periods for ATR calculation"
            )
        ]
    
    def _get_description(self) -> str:
        return "Average True Range - volatility indicator measuring market volatility"
    
    def _get_category(self) -> str:
        return "volatility"
    
    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        period = kwargs['period']
        atr = simple_atr(data['high'], data['low'], data['close'], period)
        return {'atr': atr}


class Ichimoku(BaseIndicator):
    """Ichimoku Kinko Hyo indicator."""

    def _define_parameters(self) -> List[IndicatorParameter]:
        return [
            IndicatorParameter(
                name="tenkan_period",
                type=int,
                default=9,
                min_value=1,
                max_value=50,
                description="Tenkan-sen (Conversion Line) period"
            ),
            IndicatorParameter(
                name="kijun_period",
                type=int,
                default=26,
                min_value=1,
                max_value=100,
                description="Kijun-sen (Base Line) period"
            ),
            IndicatorParameter(
                name="senkou_span_b_period",
                type=int,
                default=52,
                min_value=1,
                max_value=200,
                description="Senkou Span B period"
            ),
            IndicatorParameter(
                name="chikou_span_period",
                type=int,
                default=26,
                min_value=1,
                max_value=100,
                description="Chikou Span displacement period"
            ),
            IndicatorParameter(
                name="tenkan_color",
                type=str,
                default="#ff0000",
                description="Tenkan-sen line color",
                is_color=True
            ),
            IndicatorParameter(
                name="kijun_color",
                type=str,
                default="#0000ff",
                description="Kijun-sen line color",
                is_color=True
            ),
            IndicatorParameter(
                name="senkou_span_a_color",
                type=str,
                default="#00ff00",
                description="Senkou Span A line color",
                is_color=True
            ),
            IndicatorParameter(
                name="senkou_span_b_color",
                type=str,
                default="#ff8000",
                description="Senkou Span B line color",
                is_color=True
            ),
            IndicatorParameter(
                name="chikou_color",
                type=str,
                default="#800080",
                description="Chikou Span line color",
                is_color=True
            )
        ]

    def _get_description(self) -> str:
        return "Ichimoku Kinko Hyo - comprehensive trend and momentum indicator with 5 lines"

    def _get_category(self) -> str:
        return "trend"

    def _calculate(self, data: pd.DataFrame, **kwargs) -> Dict[str, pd.Series]:
        tenkan_period = kwargs['tenkan_period']
        kijun_period = kwargs['kijun_period']
        senkou_span_b_period = kwargs['senkou_span_b_period']
        chikou_span_period = kwargs['chikou_span_period']

        return simple_ichimoku(
            high=data['high'],
            low=data['low'],
            close=data['close'],
            tenkan_period=tenkan_period,
            kijun_period=kijun_period,
            senkou_span_b_period=senkou_span_b_period,
            chikou_span_period=chikou_span_period
        )


class TechnicalIndicators:
    """
    Factory class for technical indicators.
    """
    
    # Available indicators
    INDICATORS = {
        'SMA': SMA,
        'EMA': EMA,
        'RSI': RSI,
        'MACD': MACD,
        'BollingerBands': BollingerBands,
        'Stochastic': Stochastic,
        'ATR': ATR,
        'Ichimoku': Ichimoku,
        'KalmanTrendLevels': KalmanTrendLevels
    }
    
    @classmethod
    def get_available_indicators(cls) -> Dict[str, str]:
        """Get list of available indicators with descriptions."""
        indicators = {}
        for name, indicator_class in cls.INDICATORS.items():
            instance = indicator_class()
            indicators[name] = instance.description
        return indicators
    
    @classmethod
    def get_indicator(cls, name: str) -> BaseIndicator:
        """
        Get indicator instance by name.
        
        Args:
            name: Indicator name
            
        Returns:
            Indicator instance
            
        Raises:
            ValueError: If indicator not found
        """
        if name not in cls.INDICATORS:
            available = list(cls.INDICATORS.keys())
            raise ValueError(f"Indicator '{name}' not found. Available: {available}")
        
        return cls.INDICATORS[name]()
    
    @classmethod
    def get_indicators_by_category(cls) -> Dict[str, List[str]]:
        """Get indicators grouped by category."""
        categories = {}
        
        for name, indicator_class in cls.INDICATORS.items():
            instance = indicator_class()
            category = instance.category
            
            if category not in categories:
                categories[category] = []
            
            categories[category].append(name)
        
        return categories
