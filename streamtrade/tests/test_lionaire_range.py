"""
Test cases for Lionaire - Range indicator.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from streamtrade.indicators.custom.lionaire_range import LionaireRange


class TestLionaireRange:
    """Test cases for Lionaire - Range indicator."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample OHLCV data for testing."""
        dates = pd.date_range(start='2023-01-01', periods=300, freq='D')
        
        # Generate synthetic trending price data with volatility
        np.random.seed(42)
        base_price = 100
        trend = np.linspace(0, 20, 300)  # Upward trend
        volatility = np.random.normal(0, 2, 300)
        
        close_prices = base_price + trend + volatility
        
        # Generate OHLC from close prices
        high_prices = close_prices + np.random.uniform(0.5, 3, 300)
        low_prices = close_prices - np.random.uniform(0.5, 3, 300)
        open_prices = close_prices + np.random.uniform(-1.5, 1.5, 300)
        volume = np.random.randint(1000, 10000, 300)
        
        data = pd.DataFrame({
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volume
        }, index=dates)
        
        return data
    
    @pytest.fixture
    def indicator(self):
        """Create Lionaire Range indicator instance."""
        return LionaireRange()
    
    def test_indicator_initialization(self, indicator):
        """Test indicator initialization."""
        assert indicator.name == "LionaireRange"
        assert indicator.category == "trend"
        assert "Lionaire - Range" in indicator.description
        
        # Check parameters
        param_names = [p.name for p in indicator.parameters]
        expected_params = [
            'calculation_length', 'atr_multiplier_factor', 'atr_calculation_method',
            'price_source', 'upper_color', 'lower_color', 'middle_color',
            'fill_transparency', 'enable_fills', 'show_middle_lines'
        ]
        
        for param in expected_params:
            assert param in param_names, f"Missing parameter: {param}"
    
    def test_parameter_validation(self, indicator):
        """Test parameter validation."""
        # Test calculation_length parameter
        calc_length_param = next(p for p in indicator.parameters if p.name == "calculation_length")
        assert calc_length_param.min_value == 2
        assert calc_length_param.max_value == 1000
        assert calc_length_param.default == 200
        
        # Test atr_multiplier_factor parameter
        atr_mult_param = next(p for p in indicator.parameters if p.name == "atr_multiplier_factor")
        assert atr_mult_param.min_value == 0.1
        assert atr_mult_param.max_value == 20.0
        assert atr_mult_param.default == 6.0
        
        # Test atr_calculation_method options
        atr_method_param = next(p for p in indicator.parameters if p.name == "atr_calculation_method")
        expected_methods = ["Standard ATR", "HMA ATR", "KAMA ATR", "SMA ATR", "EMA ATR"]
        assert atr_method_param.options == expected_methods
        assert atr_method_param.default == "KAMA ATR"
    
    def test_price_source_calculation(self, indicator, sample_data):
        """Test price source calculations."""
        # Test different price sources
        sources = ["close", "open", "high", "low", "hl2", "hlc3", "ohlc4"]
        
        for source in sources:
            result = indicator._calculate_price_source(sample_data, source)
            assert isinstance(result, pd.Series)
            assert len(result) == len(sample_data)
            assert not result.isna().all(), f"All NaN values for source: {source}"
        
        # Test specific calculations
        hl2_result = indicator._calculate_price_source(sample_data, "hl2")
        expected_hl2 = (sample_data['high'] + sample_data['low']) / 2
        pd.testing.assert_series_equal(hl2_result, expected_hl2)
        
        hlc3_result = indicator._calculate_price_source(sample_data, "hlc3")
        expected_hlc3 = (sample_data['high'] + sample_data['low'] + sample_data['close']) / 3
        pd.testing.assert_series_equal(hlc3_result, expected_hlc3)
    
    def test_true_range_calculation(self, indicator, sample_data):
        """Test True Range calculation."""
        tr = indicator._calculate_true_range(sample_data)

        assert isinstance(tr, pd.Series)
        assert len(tr) == len(sample_data)

        # Check non-negative values (excluding NaN)
        valid_tr = tr.dropna()
        assert (valid_tr >= 0).all(), "True Range should be non-negative"
        assert not tr.isna().all(), "True Range should not be all NaN"

        # First value might be NaN due to shift
        assert not tr.iloc[1:].isna().all(), "True Range should have valid values after first"
    
    def test_atr_calculation_methods(self, indicator, sample_data):
        """Test different ATR calculation methods."""
        tr = indicator._calculate_true_range(sample_data)
        length = 20
        
        methods = ["Standard ATR", "HMA ATR", "KAMA ATR", "SMA ATR", "EMA ATR"]
        
        for method in methods:
            atr_result = indicator._calculate_atr_smoothed(tr, length, method)
            assert isinstance(atr_result, pd.Series)
            assert len(atr_result) == len(tr)

            # Check non-negative values (excluding NaN)
            valid_atr = atr_result.dropna()
            assert (valid_atr >= 0).all(), f"ATR should be non-negative for method: {method}"

            # Check that we have some valid values
            valid_count = atr_result.notna().sum()
            assert valid_count > 0, f"No valid ATR values for method: {method}"
    
    def test_adaptive_average_calculation(self, indicator, sample_data):
        """Test adaptive average calculation."""
        price_source = sample_data['close']
        tr = indicator._calculate_true_range(sample_data)
        atr_smoothed = indicator._calculate_atr_smoothed(tr, 20, "Standard ATR")
        
        adaptive_avg = indicator._calculate_adaptive_average(price_source, atr_smoothed, 6.0)
        
        assert isinstance(adaptive_avg, pd.Series)
        assert len(adaptive_avg) == len(price_source)
        assert not adaptive_avg.isna().all(), "Adaptive average should not be all NaN"
        
        # First value should equal first price
        assert adaptive_avg.iloc[0] == price_source.iloc[0]
        
        # Check that adaptive average shows step-like behavior (discrete movement)
        changes = adaptive_avg.diff().abs()
        zero_changes = (changes == 0).sum()
        assert zero_changes > len(adaptive_avg) * 0.3, "Adaptive average should show step-like behavior"
    
    def test_range_levels_calculation(self, indicator, sample_data):
        """Test range levels calculation."""
        adaptive_avg = sample_data['close']  # Simplified for testing
        atr_smoothed = pd.Series([2.0] * len(sample_data), index=sample_data.index)
        atr_multiplier = 6.0
        
        range_levels = indicator._calculate_range_levels(adaptive_avg, atr_smoothed, atr_multiplier)
        
        expected_keys = [
            'pr_upper_2', 'pr_mid_upper', 'pr_upper_1', 'pr_average',
            'pr_lower_1', 'pr_mid_lower', 'pr_lower_2'
        ]
        
        for key in expected_keys:
            assert key in range_levels, f"Missing range level: {key}"
            assert isinstance(range_levels[key], pd.Series)
            assert len(range_levels[key]) == len(adaptive_avg)
        
        # Test level ordering (upper levels should be higher than lower levels)
        assert (range_levels['pr_upper_2'] >= range_levels['pr_upper_1']).all()
        assert (range_levels['pr_upper_1'] >= range_levels['pr_average']).all()
        assert (range_levels['pr_average'] >= range_levels['pr_lower_1']).all()
        assert (range_levels['pr_lower_1'] >= range_levels['pr_lower_2']).all()
    
    def test_calculate_basic(self, indicator, sample_data):
        """Test basic calculation with default parameters."""
        result = indicator.calculate(sample_data)

        assert result.metadata.get('calculation_success', False), f"Calculation failed: {result.metadata.get('error', 'Unknown error')}"
        assert isinstance(result.data, dict)
        
        expected_keys = [
            'pr_upper_2', 'pr_mid_upper', 'pr_upper_1', 'pr_average',
            'pr_lower_1', 'pr_mid_lower', 'pr_lower_2'
        ]
        
        for key in expected_keys:
            assert key in result.data, f"Missing result key: {key}"
            assert isinstance(result.data[key], pd.Series)
            assert len(result.data[key]) == len(sample_data)
    
    def test_calculate_custom_parameters(self, indicator, sample_data):
        """Test calculation with custom parameters."""
        custom_params = {
            'calculation_length': 50,
            'atr_multiplier_factor': 4.0,
            'atr_calculation_method': 'EMA ATR',
            'price_source': 'hlc3'
        }

        result = indicator.calculate(sample_data, **custom_params)

        assert result.metadata.get('calculation_success', False), f"Calculation failed: {result.metadata.get('error', 'Unknown error')}"
        assert isinstance(result.data, dict)
        
        # Verify that custom parameters were used
        assert result.parameters.get('calculation_length') == 50
        assert result.parameters.get('atr_multiplier_factor') == 4.0
        assert result.parameters.get('atr_calculation_method') == 'EMA ATR'
        assert result.parameters.get('price_source') == 'hlc3'
    
    def test_missing_data_columns(self, indicator):
        """Test behavior with missing required columns."""
        incomplete_data = pd.DataFrame({
            'close': [100, 101, 102],
            'volume': [1000, 1100, 1200]
        })

        result = indicator.calculate(incomplete_data)
        assert not result.metadata.get('calculation_success', True)
        # Base indicator validates input data format, so error message will be generic
        assert "Invalid input data format" in result.metadata.get('error', '')
    
    def test_empty_data(self, indicator):
        """Test behavior with empty data."""
        empty_data = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
        
        result = indicator.calculate(empty_data)
        # Should handle empty data gracefully
        assert isinstance(result.data, dict)
    
    def test_nan_handling(self, indicator, sample_data):
        """Test handling of NaN values in data."""
        # Introduce some NaN values
        data_with_nan = sample_data.copy()
        data_with_nan.loc[data_with_nan.index[10:15], 'high'] = np.nan
        data_with_nan.loc[data_with_nan.index[20:25], 'close'] = np.nan
        
        result = indicator.calculate(data_with_nan)
        
        # Should handle NaN values gracefully
        assert isinstance(result.data, dict)
        
        # Check that we still get some valid results
        for key in ['pr_average', 'pr_upper_1', 'pr_lower_1']:
            valid_count = result.data[key].notna().sum()
            assert valid_count > 0, f"No valid values for {key} with NaN data"
